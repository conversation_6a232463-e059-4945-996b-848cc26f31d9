# Mega Mall E-commerce Platform

Mega Mall is a comprehensive e-commerce platform built with Next.js, providing a seamless shopping experience for users. The platform offers a wide range of features, including user authentication, product browsing, shopping cart functionality, order management, and an admin panel with full CRUD operations.

## Technology Stack

- **Frontend**: Next.js, TypeScript, React, Tailwind CSS, Lucide
- **Backend**: Next.js API Routes, Axios, JWT
- **Database**: MongoDB
- **Development Tools**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Lint Staged

## Features

### User Management

- User authentication (login, register, forgot password)
- User profile management (update profile, change password)

### Product Management

- Product browsing (search, filter, sort)
- Product details page
- Product suggestions using AI

### Shopping Cart

- Add, remove, update items in cart
- Calculate total cost and apply discounts

### Order Management

- Create, view, cancel orders
- Order tracking

### Admin Panel

- User management (CRUD operations)
- Product management (CRUD operations)
- Order management (CRUD operations)
- Role-based access control

## Installation Instructions

### Prerequisites

- Node.js 14 or higher
- Yarn or npm package manager

### Setup

1. Clone the repository using the following command:

